<?php

namespace App\Filament\Resources\AgentResource\Pages;

use App\Enums\CollectionType;
use App\Features\AgentMedia;
use App\Filament\Pages\App\Analytics\AgentAnalytics;
use App\Filament\Resources\AgentResource;
use App\Filament\Resources\AgentResource\Actions\LocaleSwitcher;
use App\Filament\Traits\HasColumns;
use App\Filament\Traits\HasFields;
use App\Filament\Traits\HasFilters;
use App\Jobs\TranslateModelJob;
use App\Models\Agent;
use App\Models\AgentQuestion;
use App\Models\Collection;
use App\Models\Model;
use App\Services\Formatter;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use Awcodes\Shout\Components\Shout;
use Carbon\Carbon;
use Closure;
use dacoto\DomainValidator\Validator\Domain;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Forms;
use Filament\Forms\Components\ColorPicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord\Concerns\Translatable;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\IconPosition;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\Arr;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Laravel\Pennant\Feature;
use Livewire\Component;
use RalphJSmit\Filament\RecordFinder\Forms\Components\RecordFinder;

class EditFrontend extends EditRecordTemplate
{
    use HasColumns;
    use HasFilters;

    protected static ?string $navigationLabel = 'Frontend';

    protected static ?string $navigationIcon = '';

    public function form(Form $form): Form
    {

        $canAdminister = Gatekeeper::userCanAdminister(Agent::class);

        return $form
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        static::getBrandingTab(),
                        static::getMessagingTab(),
                        static::getAttributionTab(),
                        static::getMetaTab(),
                        static::getMediaTab(),
                        static::getEmbedTab(),
                        static::getSecurityTab(),
                        static::getAdvancedTab(),
                    ])
            ])
            ->columns(1)
        ;

    }

    protected static function getSecurityTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Security')
            ->schema([
                Grid::make(['default' => 1, 'md' => 2])
                    ->relationship('frontend')
                    ->schema([
                        static::getFormFieldActive(
                            'Require users to enter a username and password to access the Agent.',
                            'has_basic_auth',
                            'Enable Password Protection'
                        )
                            ->live()
                            ->columnSpan(2)
                        ,
                        TextInput::make('basic_auth_user')
                            ->label('Username')
                            ->maxLength(50)
                            ->required(fn (Get $get) => $get('has_basic_auth'))
                            ->disabled(fn (Get $get) => ! $get('has_basic_auth'))
                        ,
                        TextInput::make('basic_auth_password')
                            ->label('Password')
                            ->maxLength(250)
                            ->password()
                            ->autocomplete(false)
                            ->revealable()
                            ->required(fn (Get $get) => $get('has_basic_auth'))
                            ->disabled(fn (Get $get) => ! $get('has_basic_auth'))
                        ,
                    ])
                ,
            ])
            ;
    }

    protected static function getMessagingTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Messaging')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([

                Fieldset::make('intro')
                    ->label('Intro')
                    ->relationship('frontend')
                    ->schema([

                        static::getFrontendTranslateActions(
                            'messaging',
                            [
                                'intro_preamble',
                                'intro_headline',
                                'intro_description',
                                'questions_title',
                                'footer_text',
                                'footer_cta_label',
                                'footer_cta_url',
                            ]
                        )
                            ->columnSpan(2)
                        ,
                        Forms\Components\Group::make()
                            ->schema([
                                TextInput::make('intro_preamble')
                                    ->label('Intro Preamble')
                                    ->live(onBlur: true),
                                TextInput::make('intro_headline')
                                    ->label('Intro Headline')
                                    ->live(onBlur: true),
                                TextInput::make('questions_title')
                                    ->label('Sample Questions Title')
                                    ->live(onBlur: true),
                            ])
                            ->columnSpan(['default' => 2, 'md' => 1])
                        ,
                        Forms\Components\Group::make()
                            ->schema([
                                Textarea::make('intro_description')
                                    ->label('Intro Description')
                                    ->rows(5)
                                    ->live(onBlur: true),
                            ])
                            ->columnSpan(['default' => 2, 'md' => 1])
                        ,

                    ])
                ,

                Fieldset::make('footer')
                    ->label('Footer')
                    ->relationship('frontend')
                    ->schema([
                        Forms\Components\TextInput::make('footer_text')
                            ->label('Footer Text')
                            ->maxLength(100)
                            ->columnSpan(2)
                        ,
                        static::getFormFieldActive(
                            false,
                            'hide_footer_cta',
                            'Hide CTA Button in Footer'
                        )
                            ->live()
                            ->columnSpan(2)
                        ,
                        static::getFormFieldUrl('footer_cta_url', 'Footer CTA URL')
                            ->disabled(fn (Get $get) => $get('hide_footer_cta'))
                        ,
                        Forms\Components\TextInput::make('footer_cta_label')
                            ->label('Footer CTA Label')
                            ->maxLength(50)
                            ->disabled(fn (Get $get) => $get('hide_footer_cta'))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: "Defaults to 'Give'"
                            )
                        ,

                    ])
                ,

            ])
            ;
    }

    protected static function getAttributionTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Attribution')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([

                Grid::make(['default' => 1, 'md' => 2])
                    ->relationship('frontend')
                    ->schema([

                        static::getFrontendTranslateActions(
                            'attribution',
                            [
                                'creator_name',
                                'creator_url',
                                'creator_description',
                            ]
                        )
                            ->columnSpan(2)
                        ,
                        TextInput::make('creator_name')
                            ->label('Creator Name')
                            ->maxLength(100)
                        ,
                        static::getFormFieldUrl('creator_url', 'Creator URL'),
                        static::getFormFieldHtmlEditor('creator_description', 'Creator Description')
                            ->columnSpan(2)
                        ,
                    ])
                ,
            ])
            ;
    }

    protected static function getBrandingTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Branding')
            ->schema([

                Fieldset::make('Colors')
                    ->relationship('frontend')
                    ->schema([
                        static::getFormFieldSelect('theme', ['dark' => 'Dark', 'light' => 'Light'], 'Theme')
                            ->default('dark')
                        ,
                        ColorPicker::make('primary_color')
                            ->label('Primary Color')
                            ->required()
                            ->default(env('AGENT_DEFAULT_PRIMARY_COLOR'))
                        ,
                        ColorPicker::make('background_color')
                            ->label('Background Color')
                        ,
                    ])
                ,

                Fieldset::make('Images')
                    ->relationship('frontend')
                    ->schema([
                        static::getFormFieldImage('agents/images', 'image_path', 'Logo'),
                        Forms\Components\FileUpload::make('background_path')
                            ->label('Background Image / Video')
                            ->directory('agents/backgrounds')
                            ->maxSize(10240)
                            ->acceptedFileTypes([
                                'image/*',
                                'video/*',
                            ])
                            ->helperText('Up to 10 MB')
                        ,
                    ])
                ,

                Fieldset::make('Fonts')
                    ->relationship('frontend')
                    ->schema([
                        static::getFormFieldUrl('display_font_url', 'Headline Font URL')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Any web-accessible font URL may be entered here — including a Google font.',
                            )
                            ->live()
                        ,
                        static::getFormFieldUrl('body_font_url', 'Body Font URL')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Any web-accessible font URL may be entered here — including a Google font.',
                            )
                            ->live()
                        ,
                        TextInput::make('display_font_name')
                            ->label('Display Font Name')
                            ->maxLength(100)
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The name of the font. Google Font names will be automatically added.',
                            )
                        ,
                        TextInput::make('body_font_name')
                            ->label('Body Font Name')
                            ->maxLength(100)
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The name of the font. Google Font names will be automatically added.',
                            )
                        ,
                    ])
                ,
            ])
            ;
    }

    protected static function getMediaTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Media')
            ->schema([
                Grid::make(['default' => 1, 'md' => 2])
                    ->relationship('frontend')
                    ->schema([
                        static::getFormFieldActive(
                            '
                                When enabled, each response will include applicable media (videos and/or podcasts).
                                You may select specific media Collections to draw on, or let any approved media Collection to be used.
                            ',
                            'show_media',
                            'Show Media'
                        )
                            ->live()
                        ,
                        static::getFormFieldMediaCollectionFinder()
                            ->disabled(fn (Get $get) => !$get('show_media'))
                        ,
                    ])
                ,
            ])
            ->visible(Feature::active(AgentMedia::class))
            ;
    }

    protected static function getEmbedTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Embed')
            ->schema([

                Fieldset::make('Embed Options')
                    ->relationship('frontend')
                    ->schema([
                        static::getFormFieldActive(
                            'Hide the logo, translation switcher, and language switcher when embedded.',
                            'embed_hide_header',
                            'Hide Header when Embedded'
                        ),
                    ])
                ,

                Fieldset::make('Beacon Options')
                    ->relationship('frontend')
                    ->schema([
                        ColorPicker::make('embed_icon_color')
                            ->label('Beacon Icon Background Color')
                        ,
                        static::getFormFieldImage('agents/embed_icons', 'embed_icon_path', 'Beacon Icon')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The icon that appears on the Beacon launcher.',
                            )
                        ,
                    ])
                ,

//                Fieldset::make('App')
//                    ->relationship('frontend')
//                    ->schema([
//                        ColorPicker::make('app_icon_color')
//                            ->label('App Icon Background Color')
//                        ,
//                        static::getFormFieldImage('agents/app_icons', 'app_icon_path', 'App Icon')
//                            ->hintIcon(
//                                'heroicon-s-question-mark-circle',
//                                tooltip: 'The icon that appears when saved to the home screen of a mobile device.',
//                            )
//                        ,
//                    ])
//                ,

            ])
            ;
    }

    protected static function getAdvancedTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Advanced')
            ->schema([
                Grid::make(['default' => 1, 'md' => 2])
                    ->relationship('frontend')
                    ->schema([
                        Textarea::make('custom_scripts')
                            ->label('Custom Scripts')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Here you can add analytics scripts and other custom Javascript.',
                            )
                            ->rows(10)
                            ->extraAttributes(['class'=>'font-mono'])
                            ->columnSpan(2)
                        ,
                        Textarea::make('custom_styles')
                            ->label('Custom Styles')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: "You may add custom CSS here, and it will be applied your Agent's interface."
                            )
                            ->helperText(view('components.help.agent-custom-styles'))
                            ->maxLength(1000)
                            ->rows(10)
                            ->extraAttributes(['class'=>'font-mono'])
                            ->columnSpan(2)
                        ,
                    ])
                ,
            ])
            ;
    }

    protected static function getMetaTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Meta')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([

                Grid::make(['default' => 1, 'md' => 2])
                    ->relationship('frontend')
                    ->schema([

                        static::getFrontendTranslateActions(
                            'meta',
                            [
                                'meta_title',
                                'meta_description',
                                'meta_keywords',
                            ]
                        )
                            ->columnSpan(2)
                        ,

                        TextInput::make('meta_title')
                            ->label('Meta Title')
                            ->maxLength(100),
                        static::getFormFieldImage('agents/favicons', 'favicon_path', 'Favicon')
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'The icon that appears in the browser tab.',
                            )
                        ,
                        Textarea::make('meta_description')
                            ->label('Meta Description')
                            ->rows(5)
                            ->maxLength(500)
                            ->columnSpan(2)
                        ,
                        Textarea::make('meta_keywords')
                            ->label('Meta Keywords')
                            ->rows(5)
                            ->maxLength(500)
                            ->columnSpan(2)
                        ,
                    ])
                ,
            ])
        ;

    }

    protected static function getFrontendTranslateActions(string $tab, array $fields): Forms\Components\Actions
    {
        $defaultLanguage = config('agent.languages')[env('AGENT_DEFAULT_LANGUAGE')];

        return Forms\Components\Actions::make([

            Forms\Components\Actions\Action::make("{$tab}_translate")
                ->extraAttributes(['id' => btn_id("translate-{$tab}")])
                ->label('Translate All')
                ->icon('heroicon-s-language')
                ->outlined()
                ->action(function (Agent $record, $livewire, Get $get, Set $set) use ($fields) {
                    $lang = $livewire->getActiveActionsLocale();
                    $frontend = $record->frontend;
                    if ($frontend) {
                        foreach ($fields as $field) {
                            $set(
                                $field,
                                $livewire->translateFormField(
                                    $lang,
                                    $get($field),
                                    $frontend->getTranslation($field, $lang)
                                )
                            );
                        }
                    }
                })
            ,

            Forms\Components\Actions\Action::make("{$tab}_reset_translation")
                ->extraAttributes(['id' => btn_id("reset-translation-{$tab}")])
                ->label("Reset All to {$defaultLanguage}")
                ->icon('heroicon-s-arrow-path')
                ->outlined()
                ->action(function (Agent $record, $livewire, Get $get, Set $set) use ($fields) {
                    $frontend = $record->frontend;
                    if ($frontend) {
                        foreach ($fields as $field) {
                            $set(
                                $field,
                                $frontend->getTranslation($field, env('AGENT_DEFAULT_LANGUAGE'))
                            );
                        }
                    }
                })
            ,

        ])
            ->alignment(Alignment::Center)
        ;
    }

    protected static function getFormFieldMediaCollectionFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'mediaCollections',
            Collection::class,
            Collection::whereIn('type', ['channel', 'podcast']),
            fn () => [
                static::getTableColumnName(),
                static::getTableColumnType(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
                static::getTableFilterType(Arr::only(CollectionType::asOptions(), ['channel', 'podcast'])),
                static::getTableFilterLanguage(),
                static::getTableFilterRelation('categories'),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
            ],
            function (Collection $collection) {
                $type = Labeller::mutate($collection->type->value);
                return new HtmlString("<span class='font-bold'>{$collection->name}</span> [{$type}]");
            },
            false,
            'Media Collections',
            'Media added to selected Collections will automatically be included.',
            'Select Media Collections'
        );
    }


}
